import torch
import torch.nn as nn


def volume_preservation_loss(displacement_field):
    """计算体积保持约束损失（雅可比行列式约束）
    Args:
        displacement_field: [H, W, 2] 位移场
    Returns:
        loss: 标量损失值
    """
    H, W = displacement_field.shape[:2]

    # 计算位移场的梯度（使用差分近似）
    # 对x方向位移求x和y方向的梯度
    dx = displacement_field[:, :, 0]  # [H, W]
    dy = displacement_field[:, :, 1]  # [H, W]

    # 计算x方向的梯度 ∂dx/∂x (沿着列方向)
    dx_dx = torch.zeros_like(dx)
    dx_dx[:, 1:] = dx[:, 1:] - dx[:, :-1]  # 前向差分
    dx_dx[:, 0] = dx_dx[:, 1]  # 边界处理

    # 计算y方向的梯度 ∂dx/∂y (沿着行方向)
    dx_dy = torch.zeros_like(dx)
    dx_dy[1:, :] = dx[1:, :] - dx[:-1, :]  # 前向差分
    dx_dy[0, :] = dx_dy[1, :]  # 边界处理

    # 计算x方向的梯度 ∂dy/∂x (沿着列方向)
    dy_dx = torch.zeros_like(dy)
    dy_dx[:, 1:] = dy[:, 1:] - dy[:, :-1]  # 前向差分
    dy_dx[:, 0] = dy_dx[:, 1]  # 边界处理

    # 计算y方向的梯度 ∂dy/∂y (沿着行方向)
    dy_dy = torch.zeros_like(dy)
    dy_dy[1:, :] = dy[1:, :] - dy[:-1, :]  # 前向差分
    dy_dy[0, :] = dy_dy[1, :]  # 边界处理

    # 构建雅可比矩阵的行列式
    # J = [[1 + ∂dx/∂x, ∂dx/∂y], [∂dy/∂x, 1 + ∂dy/∂y]]
    # det(J) = (1 + ∂dx/∂x)(1 + ∂dy/∂y) - ∂dx/∂y * ∂dy/∂x
    jacobian_det = (1 + dx_dx) * (1 + dy_dy) - dx_dy * dy_dx

    # 计算偏离1的程度（理想情况下雅可比行列式应该接近1）
    volume_loss = torch.mean((jacobian_det - 1.0) ** 2)

    return volume_loss


def smoothness_loss(displacement_field):
    """计算平滑性约束损失（拉普拉斯平滑损失）
    Args:
        displacement_field: [H, W, 2] 位移场
    Returns:
        loss: 标量损失值
    """
    H, W = displacement_field.shape[:2]

    # 计算二阶导数（拉普拉斯算子）使用差分近似
    # 对x方向位移
    dx = displacement_field[:, :, 0]  # [H, W]

    # 计算一阶导数
    dx_x = torch.zeros_like(dx)
    dx_x[:, 1:] = dx[:, 1:] - dx[:, :-1]
    dx_x[:, 0] = dx_x[:, 1]

    dx_y = torch.zeros_like(dx)
    dx_y[1:, :] = dx[1:, :] - dx[:-1, :]
    dx_y[0, :] = dx_y[1, :]

    # 计算二阶导数
    dx_xx = torch.zeros_like(dx)
    dx_xx[:, 1:] = dx_x[:, 1:] - dx_x[:, :-1]
    dx_xx[:, 0] = dx_xx[:, 1]

    dx_yy = torch.zeros_like(dx)
    dx_yy[1:, :] = dx_y[1:, :] - dx_y[:-1, :]
    dx_yy[0, :] = dx_yy[1, :]

    laplacian_dx = dx_xx + dx_yy

    # 对y方向位移
    dy = displacement_field[:, :, 1]  # [H, W]

    # 计算一阶导数
    dy_x = torch.zeros_like(dy)
    dy_x[:, 1:] = dy[:, 1:] - dy[:, :-1]
    dy_x[:, 0] = dy_x[:, 1]

    dy_y = torch.zeros_like(dy)
    dy_y[1:, :] = dy[1:, :] - dy[:-1, :]
    dy_y[0, :] = dy_y[1, :]

    # 计算二阶导数
    dy_xx = torch.zeros_like(dy)
    dy_xx[:, 1:] = dy_x[:, 1:] - dy_x[:, :-1]
    dy_xx[:, 0] = dy_xx[:, 1]

    dy_yy = torch.zeros_like(dy)
    dy_yy[1:, :] = dy_y[1:, :] - dy_y[:-1, :]
    dy_yy[0, :] = dy_yy[1, :]

    laplacian_dy = dy_xx + dy_yy

    # 计算拉普拉斯范数
    smooth_loss = torch.mean(laplacian_dx ** 2) + torch.mean(laplacian_dy ** 2)

    return smooth_loss


class PhysicsConstrainedLoss(nn.Module):
    """物理约束损失组合类"""
    
    def __init__(self, sparsity_lambda=0.001, volume_lambda=0.01, smooth_lambda=0.005):
        super().__init__()
        self.sparsity_lambda = sparsity_lambda
        self.volume_lambda = volume_lambda
        self.smooth_lambda = smooth_lambda
        
    def forward(self, bn_loss, alpha_weights, displacement_field):
        """计算总损失
        Args:
            bn_loss: BN统计量对齐损失
            alpha_weights: [num_points] 显著性权重
            displacement_field: [H, W, 2] 位移场
        Returns:
            total_loss: 总损失
            loss_dict: 各项损失的字典
        """
        # L1稀疏化损失
        sparsity_loss = torch.mean(torch.abs(alpha_weights))
        
        # 体积保持损失
        volume_loss = volume_preservation_loss(displacement_field)
        
        # 平滑性损失
        smooth_loss = smoothness_loss(displacement_field)
        
        # 总损失
        total_loss = (bn_loss + 
                     self.sparsity_lambda * sparsity_loss +
                     self.volume_lambda * volume_loss +
                     self.smooth_lambda * smooth_loss)
        
        # 返回损失字典用于监控
        loss_dict = {
            'bn_loss': bn_loss.item(),
            'sparsity_loss': sparsity_loss.item(),
            'volume_loss': volume_loss.item(),
            'smooth_loss': smooth_loss.item(),
            'total_loss': total_loss.item()
        }
        
        return total_loss, loss_dict
